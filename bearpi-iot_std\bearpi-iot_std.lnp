--cpu=Cortex-M4.fp.sp --branchpatch=sdcomp-29491-629360
"bearpi-iot_std\startup_stm32l431xx.o"
"bearpi-iot_std\main.o"
"bearpi-iot_std\gpio.o"
"bearpi-iot_std\adc.o"
"bearpi-iot_std\dac.o"
"bearpi-iot_std\dma.o"
"bearpi-iot_std\i2c.o"
"bearpi-iot_std\usart.o"
"bearpi-iot_std\quadspi.o"
"bearpi-iot_std\sdmmc.o"
"bearpi-iot_std\spi.o"
"bearpi-iot_std\tim.o"
"bearpi-iot_std\stm32l4xx_it.o"
"bearpi-iot_std\stm32l4xx_hal_msp.o"
"bearpi-iot_std\stm32l4xx_hal_adc.o"
"bearpi-iot_std\stm32l4xx_hal_adc_ex.o"
"bearpi-iot_std\stm32l4xx_hal.o"
"bearpi-iot_std\stm32l4xx_hal_i2c.o"
"bearpi-iot_std\stm32l4xx_hal_i2c_ex.o"
"bearpi-iot_std\stm32l4xx_hal_rcc.o"
"bearpi-iot_std\stm32l4xx_hal_rcc_ex.o"
"bearpi-iot_std\stm32l4xx_hal_flash.o"
"bearpi-iot_std\stm32l4xx_hal_flash_ex.o"
"bearpi-iot_std\stm32l4xx_hal_flash_ramfunc.o"
"bearpi-iot_std\stm32l4xx_hal_gpio.o"
"bearpi-iot_std\stm32l4xx_hal_dma.o"
"bearpi-iot_std\stm32l4xx_hal_dma_ex.o"
"bearpi-iot_std\stm32l4xx_hal_pwr.o"
"bearpi-iot_std\stm32l4xx_hal_pwr_ex.o"
"bearpi-iot_std\stm32l4xx_hal_cortex.o"
"bearpi-iot_std\stm32l4xx_hal_exti.o"
"bearpi-iot_std\stm32l4xx_hal_dac.o"
"bearpi-iot_std\stm32l4xx_hal_dac_ex.o"
"bearpi-iot_std\stm32l4xx_hal_uart.o"
"bearpi-iot_std\stm32l4xx_hal_uart_ex.o"
"bearpi-iot_std\stm32l4xx_hal_qspi.o"
"bearpi-iot_std\stm32l4xx_ll_sdmmc.o"
"bearpi-iot_std\stm32l4xx_hal_sd.o"
"bearpi-iot_std\stm32l4xx_hal_sd_ex.o"
"bearpi-iot_std\stm32l4xx_hal_spi.o"
"bearpi-iot_std\stm32l4xx_hal_spi_ex.o"
"bearpi-iot_std\stm32l4xx_hal_tim.o"
"bearpi-iot_std\stm32l4xx_hal_tim_ex.o"
"bearpi-iot_std\system_stm32l4xx.o"
"bearpi-iot_std\hzlib.o"
"bearpi-iot_std\lcd.o"
"bearpi-iot_std\flash.o"
"bearpi-iot_std\hal_qspi_flash.o"
"bearpi-iot_std\e53_ia1.o"
"bearpi-iot_std\fatfs.o"
"bearpi-iot_std\sd_diskio.o"
"bearpi-iot_std\bsp_driver_sd.o"
"bearpi-iot_std\syscall.o"
"bearpi-iot_std\diskio.o"
"bearpi-iot_std\ff.o"
"bearpi-iot_std\ff_gen_drv.o"
"bearpi-iot_std\cc936.o"
"bearpi-iot_std\esp8266.o"
--library_type=microlib --strict --scatter "bearpi-iot_std\bearpi-iot_std.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "bearpi-iot_std.map" -o bearpi-iot_std\bearpi-iot_std.axf