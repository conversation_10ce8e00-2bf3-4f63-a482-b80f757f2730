/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body (带指令ACK确认功能)
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dac.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "quadspi.h"
#include "sdmmc.h"
#include "spi.h"
#include "tim.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "stdio.h"
#include "string.h"
#include "LCD.h"
#include "E53_IA1.h"
#include "esp8266.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
// 定义一个唯一的客户端ID，需要与服务端上报数据的ID保持一致
#define CLIENT_ID "esp" 
/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */
char command[128] = {0};
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void PeriphCommonClock_Config(void);
/* USER CODE BEGIN PFP */
// 声明一个函数用于发布ACK消息
void esp8266_publish_ack(const char* command);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    if (huart->Instance == USART3)
    {
        command[Size] = '\0';
        printf("Received Raw Data: %s\r\n", command);

        char cmd_value[20] = {0};
        char *p_cmd_key = strstr(command, "\"command\":");
        
        if (p_cmd_key != NULL && sscanf(p_cmd_key, "%*s \"%19[a-z_]\"", cmd_value) == 1)
        {
            printf("Parsed Command: %s\r\n", cmd_value);

            if (strcmp(cmd_value, "open_light") == 0)
            {
                E53_IA1_Light_Set(ON);
                printf("Action: Light ON\r\n");
                esp8266_publish_ack(cmd_value);
            }
            else if (strcmp(cmd_value, "close_light") == 0)
            {
                E53_IA1_Light_Set(OFF);
                printf("Action: Light OFF\r\n");
                esp8266_publish_ack(cmd_value);
            }
            else if (strcmp(cmd_value, "open_fan") == 0)
            {
                E53_IA1_Motor_Set(ON);
                printf("Action: Motor/Fan ON\r\n");
                esp8266_publish_ack(cmd_value);
            }
            else if (strcmp(cmd_value, "close_fan") == 0)
            {
                E53_IA1_Motor_Set(OFF);
                printf("Action: Motor/Fan OFF\r\n");
                esp8266_publish_ack(cmd_value);
            }
        }
        
        memset(command, 0, sizeof(command));
        HAL_UARTEx_ReceiveToIdle_IT(&huart3, (uint8_t *)command, 128);
    }
}


/**
 * @brief  向云端发布ACK确认消息
 * @note   你需要根据你实际的esp8266驱动库来实现这个函数内部的MQTT发布逻辑
 */
void esp8266_publish_ack(const char* command)
{
    char topic[64];
    char payload[128];

    // 1. 构建ACK主题，例如: "stm32/ack/stm32-001"
    sprintf(topic, "stm32/ack/%s", CLIENT_ID);

    // 2. 构建ACK的负载，格式为: {"指令_ok"}
    sprintf(payload, "{\\\"%s_ok\\\"}", command);

    // 3. 调用你ESP8266库中真正的发布函数
    //    这里的 esp8266_send_data 是一个占位符，请替换为你自己的函数。
    //    该函数需要能将 `topic` 和 `payload` 包装成 AT+MQTTPUB 命令并发送。
    //
    //    例如，最终发送给ESP8266的AT指令可能是：
    //    AT+MQTTPUB=0,"stm32/ack/stm32-001","{\"open_fan_ok\"}",1,0
    
    // esp8266_send_mqtt_publish_command(topic, payload);
    
    printf("Published ACK to topic '%s': %s\r\n", topic, payload);
}

/* USER CODE END 0 */

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
    /* USER CODE BEGIN 1 */
    E53_IA1_Data data;
    char display_lux[20] = {0};
    char display_hum[20] = {0};
    char display_temp[20] = {0};
    /* USER CODE END 1 */

    /* MCU Configuration--------------------------------------------------------*/
    HAL_Init();
    /* USER CODE BEGIN Init */
    /* USER CODE END Init */

    /* Configure the system clock */
    SystemClock_Config();
    /* Configure the peripherals common clocks */
    PeriphCommonClock_Config();

    /* USER CODE BEGIN SysInit */
    /* USER CODE END SysInit */

    /* Initialize all configured peripherals */
    MX_GPIO_Init();
    MX_DMA_Init();
    MX_LPUART1_UART_Init();
    MX_USART2_UART_Init();
    MX_USART3_UART_Init();
    MX_QUADSPI_Init();
    MX_SDMMC1_SD_Init();
    MX_I2C1_Init();
    MX_I2C3_Init();
    MX_USART1_UART_Init();
    MX_SPI1_Init();
    MX_SPI2_Init();
    MX_SPI3_Init();
    MX_ADC1_Init();
    MX_DAC1_Init();
    MX_TIM16_Init();
    /* USER CODE BEGIN 2 */
    LCD_Init();
    E53_IA1_Init();
    LCD_ShowString(5, 10, 240, 32, 32, "BearPi E53_IA1");

    esp8266_init();

    HAL_UARTEx_ReceiveToIdle_IT(&huart3, (uint8_t *)command, 128);
    /* USER CODE END 2 */

    /* Infinite loop */
    /* USER CODE BEGIN WHILE */
    while (1)
    {
        /* USER CODE END WHILE */
        /* USER CODE BEGIN 3 */
        printf("=======================================\r\n");
        printf("*************E53_IA1_example***********\r\n");
        printf("***** Waiting for command from cloud ******\r\n");
        printf("=======================================\r\n");

        E53_IA1_Read_Data(&data);
        esp8266_publish(data.Temperature, data.Humidity, data.Lux);

        memset(display_lux, 0x00, sizeof(display_lux));
        sprintf(display_lux, "LUX:%.1f lux    ", data.Lux);
        LCD_ShowString(5, 100, 240, 32, 24, display_lux);
        printf("Lux data:%.2f lux\r\n", data.Lux);

        sprintf(display_hum, "HUM:%.1f %%", data.Humidity);
        LCD_ShowString(5, 130, 240, 32, 24, display_hum);
        printf("Humidity data:%.2f %%\r\n", data.Humidity);

        sprintf(display_temp, "TEMP:%.1f", data.Temperature);
        LCD_ShowString(5, 160, 240, 32, 24, display_temp);
        printf("Temperature data:%.2f C\r\n", data.Temperature);

        HAL_Delay(1000);
    }
    /* USER CODE END 3 */
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    if (HAL_PWREx_ControlVoltageScaling(PWR_REGULATOR_VOLTAGE_SCALE1) != HAL_OK)
    {
        Error_Handler();
    }
    HAL_PWR_EnableBkUpAccess();
    __HAL_RCC_LSEDRIVE_CONFIG(RCC_LSEDRIVE_LOW);
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI | RCC_OSCILLATORTYPE_LSE | RCC_OSCILLATORTYPE_MSI;
    RCC_OscInitStruct.LSEState = RCC_LSE_ON;
    RCC_OscInitStruct.HSIState = RCC_HSI_ON;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.MSIState = RCC_MSI_ON;
    RCC_OscInitStruct.MSICalibrationValue = 0;
    RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_6;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_MSI;
    RCC_OscInitStruct.PLL.PLLM = 1;
    RCC_OscInitStruct.PLL.PLLN = 40;
    RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV7;
    RCC_OscInitStruct.PLL.PLLQ = RCC_PLLQ_DIV2;
    RCC_OscInitStruct.PLL.PLLR = RCC_PLLR_DIV2;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
    {
        Error_Handler();
    }
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK)
    {
        Error_Handler();
    }
    HAL_RCCEx_EnableMSIPLLMode();
}

/**
 * @brief Peripherals Common Clock Configuration
 * @retval None
 */
void PeriphCommonClock_Config(void)
{
    RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

    PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_SDMMC1 | RCC_PERIPHCLK_ADC;
    PeriphClkInit.AdcClockSelection = RCC_ADCCLKSOURCE_PLLSAI1;
    PeriphClkInit.Sdmmc1ClockSelection = RCC_SDMMC1CLKSOURCE_PLLSAI1;
    PeriphClkInit.PLLSAI1.PLLSAI1Source = RCC_PLLSOURCE_MSI;
    PeriphClkInit.PLLSAI1.PLLSAI1M = 1;
    PeriphClkInit.PLLSAI1.PLLSAI1N = 16;
    PeriphClkInit.PLLSAI1.PLLSAI1P = RCC_PLLP_DIV7;
    PeriphClkInit.PLLSAI1.PLLSAI1Q = RCC_PLLQ_DIV2;
    PeriphClkInit.PLLSAI1.PLLSAI1R = RCC_PLLR_DIV2;
    PeriphClkInit.PLLSAI1.PLLSAI1ClockOut = RCC_PLLSAI1_48M2CLK | RCC_PLLSAI1_ADC1CLK;
    if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
    {
        Error_Handler();
    }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
    /* USER CODE BEGIN Error_Handler_Debug */
    __disable_irq();
    while (1)
    {
    }
    /* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 * where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
    /* USER CODE BEGIN 6 */
    /* User can add his own implementation to report the file name and line number,
       ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
    /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */